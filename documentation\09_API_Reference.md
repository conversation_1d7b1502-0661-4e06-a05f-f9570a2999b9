# API Reference - Arroyo University

## Introducción

Este documento proporciona la referencia completa de la API REST de Arroyo University. Todas las APIs siguen estándares RESTful, utilizan JSON para intercambio de datos y implementan autenticación JWT. La documentación incluye endpoints, parámetros, respuestas y ejemplos de uso.

---

## 1. Convenciones Generales

### 1.1 Base URL y Versionado
```
Base URL: https://api.arroyo.app/v1
Tenant-specific: https://{tenant}.arroyo.app/api/v1
```

### 1.2 Autenticación
| Tipo | Descripción | Header |
|------|-------------|--------|
| 🔒 **JWT** | Token de acceso requerido | `Authorization: Bearer {token}` |
| 🔑 **HMAC** | Firma para webhooks | `X-Signature: sha256={signature}` |
| 🌐 **Público** | Sin autenticación requerida | N/A |

### 1.3 Códigos de Respuesta
| Código | Significado | Uso |
|--------|-------------|-----|
| `200` | OK | Operación exitosa |
| `201` | Created | Recurso creado exitosamente |
| `204` | No Content | Operación exitosa sin contenido |
| `400` | Bad Request | Error en parámetros de entrada |
| `401` | Unauthorized | Token inválido o expirado |
| `403` | Forbidden | Sin permisos para la operación |
| `404` | Not Found | Recurso no encontrado |
| `409` | Conflict | Conflicto con estado actual |
| `422` | Unprocessable Entity | Error de validación |
| `429` | Too Many Requests | Rate limit excedido |
| `500` | Internal Server Error | Error interno del servidor |

### 1.4 Formato de Errores (RFC 7807)
```json
{
  "type": "https://api.arroyo.app/errors/validation-error",
  "title": "Validation Error",
  "status": 422,
  "detail": "The request body contains invalid data",
  "instance": "/v1/courses",
  "errors": [
    {
      "field": "title",
      "code": "required",
      "message": "Title is required"
    }
  ]
}
```

---

## 2. Autenticación y Seguridad

### 2.1 Login
```http
POST /auth/login
Content-Type: application/json
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "securePassword123",
  "mfa_code": "123456"  // Opcional si MFA habilitado
}
```

**Response (200):**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "expires_in": 900,
  "user": {
    "user_id": "123e4567-e89b-12d3-a456-426614174000",
    "email": "<EMAIL>",
    "roles": ["content_creator"],
    "tenant_id": "123e4567-e89b-12d3-a456-426614174001"
  }
}
```

### 2.2 Refresh Token
```http
POST /auth/refresh
Content-Type: application/json
```

**Request Body:**
```json
{
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

### 2.3 Logout
```http
POST /auth/logout 🔒
Authorization: Bearer {access_token}
```

---

## 3. Gestión de Tenants

### 3.1 Crear Tenant
```http
POST /tenants 🔒
Authorization: Bearer {sysadmin_token}
Content-Type: application/json
```

**Request Body:**
```json
{
  "name": "Acme Corporation",
  "description": "Corporate training platform",
  "plan": "premium",
  "admin_email": "<EMAIL>",
  "settings": {
    "branding": {
      "logo_url": "https://acme.com/logo.png",
      "primary_color": "#007bff"
    },
    "features": {
      "sso_enabled": true,
      "ai_generation_limit": 1000
    }
  }
}
```

**Response (201):**
```json
{
  "tenant_id": "123e4567-e89b-12d3-a456-426614174001",
  "name": "Acme Corporation",
  "subdomain": "acme",
  "status": "provisioning",
  "created_at": "2024-01-15T10:30:00Z",
  "onboarding_url": "https://acme.arroyo.app/onboarding?token=..."
}
```

### 3.2 Obtener Tenant
```http
GET /tenants/{tenant_id} 🔒
Authorization: Bearer {token}
```

### 3.3 Actualizar Configuración
```http
PATCH /tenants/{tenant_id} 🔒
Authorization: Bearer {admin_tenant_token}
Content-Type: application/json
```

---

## 4. Gestión de Usuarios

### 4.1 Crear Usuario
```http
POST /users 🔒
Authorization: Bearer {admin_tenant_token}
Content-Type: application/json
```

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "username": "newuser",
  "roles": ["student"],
  "groups": ["group_123"],
  "temporary": false,
  "expires_at": null,
  "send_invitation": true
}
```

**Response (201):**
```json
{
  "user_id": "123e4567-e89b-12d3-a456-426614174002",
  "email": "<EMAIL>",
  "username": "newuser",
  "verified": false,
  "invitation_sent": true,
  "invitation_expires_at": "2024-01-16T10:30:00Z",
  "created_at": "2024-01-15T10:30:00Z"
}
```

### 4.2 Listar Usuarios
```http
GET /users 🔒
Authorization: Bearer {token}
```

**Query Parameters:**
- `page`: Número de página (default: 1)
- `limit`: Elementos por página (default: 20, max: 100)
- `search`: Búsqueda por email/username
- `role`: Filtrar por rol
- `status`: active, inactive, pending

**Response (200):**
```json
{
  "users": [
    {
      "user_id": "123e4567-e89b-12d3-a456-426614174002",
      "email": "<EMAIL>",
      "username": "user",
      "roles": ["student"],
      "last_login": "2024-01-15T09:15:00Z",
      "status": "active"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 150,
    "pages": 8
  }
}
```

### 4.3 Actualizar Usuario
```http
PATCH /users/{user_id} 🔒
Authorization: Bearer {token}
Content-Type: application/json
```

---

## 5. Gestión de Cursos

### 5.1 Crear Curso
```http
POST /courses 🔒
Authorization: Bearer {content_creator_token}
Content-Type: application/json
```

**Request Body:**
```json
{
  "title": "Advanced English Communication",
  "description": "Comprehensive course for business English",
  "metadata": {
    "difficulty": "B2",
    "estimated_hours": 40,
    "prerequisites": ["basic_english"],
    "tags": ["business", "communication", "english"]
  }
}
```

**Response (201):**
```json
{
  "course_id": "123e4567-e89b-12d3-a456-426614174003",
  "title": "Advanced English Communication",
  "status": "draft",
  "created_at": "2024-01-15T10:30:00Z",
  "version": 1
}
```

### 5.2 Subir Contenido Multimedia
```http
POST /courses/{course_id}/items 🔒
Authorization: Bearer {content_creator_token}
Content-Type: multipart/form-data
```

**Form Data:**
- `file`: Archivo multimedia (max 200MB)
- `item_type`: video, audio, pdf, image
- `title`: Título del contenido
- `description`: Descripción opcional

### 5.3 Publicar Curso
```http
PATCH /courses/{course_id} 🔒
Authorization: Bearer {content_creator_token}
Content-Type: application/json
```

**Request Body:**
```json
{
  "status": "published"
}
```

---

## 6. Banco de Preguntas

### 6.1 Crear Pregunta Manual
```http
POST /questions 🔒
Authorization: Bearer {content_creator_token}
Content-Type: application/json
```

**Request Body:**
```json
{
  "item_type": "writing",
  "prompt": "Describe a project management methodology you have used and explain its advantages and disadvantages.",
  "difficulty": "intermediate",
  "metadata": {
    "estimated_time_minutes": 15,
    "word_count_target": 150,
    "rubric": {
      "content": {"weight": 0.4, "max_score": 5},
      "organization": {"weight": 0.3, "max_score": 5},
      "clarity": {"weight": 0.3, "max_score": 5}
    }
  },
  "tags": ["project_management", "methodology", "analysis"]
}
```

### 6.2 Generar Preguntas con IA
```http
POST /questions/generate 🔒
Authorization: Bearer {content_creator_token}
Content-Type: application/json
```

**Request Body:**
```json
{
  "type": "listening",
  "prompt": "Create questions about hobbies and leisure activities",
  "count": 5,
  "difficulty": "B1",
  "style": "conversational",
  "metadata": {
    "speakers": 2,
    "duration_target": 60,
    "accent": "neutral"
  }
}
```

**Response (202):**
```json
{
  "job_id": "123e4567-e89b-12d3-a456-426614174004",
  "status": "pending",
  "estimated_completion": "2024-01-15T10:35:00Z",
  "webhook_url": "/webhooks/generation-complete"
}
```

### 6.3 Estado de Generación
```http
GET /ai/jobs/{job_id} 🔒
Authorization: Bearer {token}
```

**Response (200):**
```json
{
  "job_id": "123e4567-e89b-12d3-a456-426614174004",
  "status": "completed",
  "progress": 100,
  "result": {
    "questions_created": 5,
    "question_ids": ["q1", "q2", "q3", "q4", "q5"],
    "tokens_used": 1250,
    "cost_usd": 0.025
  },
  "completed_at": "2024-01-15T10:34:30Z"
}
```

---

## 7. Exámenes y Evaluación

### 7.1 Crear Examen
```http
POST /exams 🔒
Authorization: Bearer {content_creator_token}
Content-Type: application/json
```

**Request Body:**
```json
{
  "course_id": "123e4567-e89b-12d3-a456-426614174003",
  "title": "English Placement Test",
  "description": "Comprehensive assessment of English proficiency",
  "time_limit_sec": 3600,
  "settings": {
    "randomize_questions": true,
    "allow_review": false,
    "attempts_allowed": 1,
    "show_results_immediately": true
  }
}
```

### 7.2 Asignar Preguntas
```http
PUT /exams/{exam_id}/items 🔒
Authorization: Bearer {content_creator_token}
Content-Type: application/json
```

**Request Body:**
```json
{
  "question_ids": [
    "123e4567-e89b-12d3-a456-426614174005",
    "123e4567-e89b-12d3-a456-426614174006"
  ],
  "order": "random"
}
```

### 7.3 Iniciar Intento de Examen
```http
POST /exam-attempts 🌐
Content-Type: application/json
```

**Request Body:**
```json
{
  "exam_id": "123e4567-e89b-12d3-a456-426614174007",
  "access_token": "temp_token_for_guest_user"
}
```

**Response (201):**
```json
{
  "attempt_id": "123e4567-e89b-12d3-a456-426614174008",
  "exam_title": "English Placement Test",
  "time_limit_sec": 3600,
  "total_questions": 25,
  "started_at": "2024-01-15T10:30:00Z",
  "expires_at": "2024-01-15T11:30:00Z"
}
```

### 7.4 Obtener Siguiente Pregunta
```http
GET /attempts/{attempt_id}/next 🔒
Authorization: Bearer {token}
```

**Response (200):**
```json
{
  "question_number": 1,
  "total_questions": 25,
  "question": {
    "question_id": "123e4567-e89b-12d3-a456-426614174005",
    "type": "writing",
    "prompt": "Describe your ideal vacation destination...",
    "time_limit_sec": 900,
    "metadata": {
      "word_count_target": 150
    }
  },
  "time_remaining_sec": 3540
}
```

### 7.5 Enviar Respuesta
```http
POST /answers 🔒
Authorization: Bearer {token}
Content-Type: application/json
```

**Request Body:**
```json
{
  "attempt_id": "123e4567-e89b-12d3-a456-426614174008",
  "question_id": "123e4567-e89b-12d3-a456-426614174005",
  "answer_data": {
    "text": "My ideal vacation destination would be Japan because...",
    "time_spent_sec": 720
  }
}
```

### 7.6 Finalizar Examen
```http
POST /attempts/{attempt_id}/finish 🔒
Authorization: Bearer {token}
```

**Response (200):**
```json
{
  "attempt_id": "123e4567-e89b-12d3-a456-426614174008",
  "status": "completed",
  "submitted_at": "2024-01-15T11:15:00Z",
  "total_time_sec": 2700,
  "questions_answered": 25,
  "preliminary_score": 78.5,
  "estimated_cefr": "B2",
  "detailed_results_available_at": "2024-01-15T11:20:00Z"
}
```

---

## 8. Resultados y Analítica

### 8.1 Obtener Resultados de Examen
```http
GET /reports/exams/{exam_id} 🔒
Authorization: Bearer {token}
```

**Query Parameters:**
- `user_id`: Filtrar por usuario específico
- `from_date`: Fecha inicio (ISO 8601)
- `to_date`: Fecha fin (ISO 8601)
- `format`: json, csv, pdf

**Response (200):**
```json
{
  "exam_id": "123e4567-e89b-12d3-a456-426614174007",
  "exam_title": "English Placement Test",
  "attempts": [
    {
      "attempt_id": "123e4567-e89b-12d3-a456-426614174008",
      "user_email": "<EMAIL>",
      "started_at": "2024-01-15T10:30:00Z",
      "completed_at": "2024-01-15T11:15:00Z",
      "total_score": 82.3,
      "cefr_level": "B2",
      "breakdown": {
        "writing": 85.0,
        "listening": 78.0,
        "speaking": 84.0
      }
    }
  ],
  "statistics": {
    "total_attempts": 156,
    "average_score": 76.8,
    "completion_rate": 94.2,
    "average_time_minutes": 48
  }
}
```

### 8.2 Dashboard de Métricas
```http
GET /metrics 🔒
Authorization: Bearer {token}
```

**Query Parameters:**
- `period`: day, week, month, year
- `metric_types`: users, exams, ai_usage, costs

---

## 9. Webhooks y Integraciones

### 9.1 Configurar Webhook
```http
POST /webhooks 🔒
Authorization: Bearer {admin_tenant_token}
Content-Type: application/json
```

**Request Body:**
```json
{
  "url": "https://your-system.com/webhooks/arroyo",
  "events": ["exam.completed", "user.created", "payment.succeeded"],
  "secret": "your_webhook_secret",
  "active": true
}
```

### 9.2 Ejemplo de Payload de Webhook
```json
{
  "event": "exam.completed",
  "timestamp": "2024-01-15T11:20:00Z",
  "data": {
    "attempt_id": "123e4567-e89b-12d3-a456-426614174008",
    "user_email": "<EMAIL>",
    "exam_title": "English Placement Test",
    "score": 82.3,
    "cefr_level": "B2"
  },
  "tenant_id": "123e4567-e89b-12d3-a456-426614174001"
}
```

---

## 10. Rate Limiting y Cuotas

### 10.1 Headers de Rate Limiting
```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1642248000
```

### 10.2 Límites por Plan
| Plan | Requests/min | AI Generations/month | Storage GB |
|------|--------------|---------------------|------------|
| Free | 100 | 50 | 1 |
| Basic | 500 | 500 | 10 |
| Premium | 2000 | 2000 | 100 |
| Enterprise | 10000 | Unlimited | 1000 |

---

## Conclusión

Esta API Reference proporciona la documentación completa para integrar con Arroyo University. Para ejemplos adicionales, SDKs y documentación interactiva, visite nuestra [documentación completa](https://docs.arroyo.app) o explore nuestro [Postman Collection](https://postman.com/arroyo-university).
