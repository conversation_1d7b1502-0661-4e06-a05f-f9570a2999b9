# Mockups - Arroyo University

Este directorio contiene los mockups de las interfaces de usuario de Arroyo University, organizados por funcionalidad y tipo de usuario.

## Estructura de Mockups

### 1. Autenticación y Onboarding
- `01_auth_login.html` - Página de login con SSO
- `02_tenant_setup_wizard.html` - Wizard de configuración inicial
- `03_user_registration.html` - Registro y verificación de usuarios

### 2. Dashboards por Rol
- `04_admin_tenant_dashboard.html` - Dashboard principal del Admin Tenant
- `05_content_creator_dashboard.html` - Dashboard del creador de contenido
- `06_student_dashboard.html` - Dashboard del estudiante (legacy)
- `07_system_admin_dashboard.html` - Dashboard del administrador del sistema
- `43_user_home_dashboard.html` - **Dashboard principal del usuario con navegación lateral**

### 3. Gestión de Contenido
- `08_course_creation.html` - Creación y edición de cursos
- `09_question_bank.html` - Banco de preguntas
- `10_ai_question_generator.html` - Generador de preguntas con IA
- `11_multimedia_upload.html` - Subida de contenido multimedia

### 4. Gestión de Exámenes
- `12_exam_creation_wizard.html` - Wizard de creación de exámenes
- `13_question_selection.html` - Selección de preguntas para exámenes
- `14_exam_configuration.html` - Configuración de exámenes

### 5. Experiencia de Examen
- `15_exam_landing.html` - Página de inicio de evaluación
- `16_exam_writing_question.html` - Pregunta tipo Writing
- `17_exam_listening_question.html` - Pregunta tipo Listening
- `18_exam_speaking_question.html` - Pregunta tipo Speaking
- `19_exam_progress.html` - Seguimiento de progreso

### 6. Corrección y Resultados
- `20_manual_scoring.html` - Interface de corrección manual
- `21_results_dashboard.html` - Dashboard de resultados
- `22_detailed_feedback.html` - Feedback detallado

### 7. Analítica y Reportes
- `23_analytics_dashboard.html` - Dashboard de analítica
- `24_export_functionality.html` - Funcionalidad de exportación
- `25_performance_metrics.html` - Métricas de rendimiento

### 8. Administración
- `26_user_management.html` - Gestión de usuarios
- `27_role_permission_management.html` - Gestión de roles y permisos
- `28_billing_subscription.html` - Gestión de facturación
- `29_system_settings.html` - Configuración del sistema

## Tecnologías Utilizadas

Los mockups están construidos con:
- **HTML5** - Estructura semántica
- **Tailwind CSS v4** - Estilos utility-first
- **JavaScript vanilla** - Interactividad básica
- **Iconos** - Heroicons y Lucide
- **Componentes** - Siguiendo el design system de la aplicación

## Características de los Mockups

- **Responsive Design** - Mobile-first approach
- **Accesibilidad** - WCAG 2.1 AA compliance
- **Multi-tenant** - Branding personalizable por tenant
- **Internacionalización** - Preparado para múltiples idiomas
- **Estados de UI** - Loading, error, success, empty states
- **Navegación Lateral** - Sidebar navigation para mejor UX
- **Estructura Modular** - Cursos con módulos y contenido flexible

## Convenciones de Diseño

### Colores
- **Primary**: Blue-600 (#2563eb)
- **Secondary**: Gray-600 (#4b5563)
- **Success**: Green-600 (#059669)
- **Warning**: Yellow-600 (#d97706)
- **Error**: Red-600 (#dc2626)

### Tipografía
- **Headings**: Inter font, font-semibold
- **Body**: Inter font, font-normal
- **Code**: JetBrains Mono

### Espaciado
- **Containers**: max-w-7xl mx-auto px-4
- **Sections**: py-8 space-y-6
- **Cards**: p-6 rounded-lg shadow-sm

## Notas de Implementación

Estos mockups sirven como referencia visual para:
1. **Desarrollo Frontend** - Estructura y componentes
2. **UX/UI Testing** - Flujos de usuario
3. **Stakeholder Review** - Validación de requisitos
4. **Documentation** - Especificaciones visuales
