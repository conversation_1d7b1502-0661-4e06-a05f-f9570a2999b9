# Seguridad y RBAC - Arroyo University

## Introducción

Este documento describe el modelo de seguridad integral de Arroyo University, incluyendo autenticación, autorización, control de acceso basado en roles (RBAC), y medidas de seguridad para cumplir con estándares internacionales como OWASP, GDPR y FERPA.

---

## 1. Arquitectura de Seguridad

### 1.1 Principios de Seguridad
- **Zero Trust**: Verificación continua de identidad y permisos
- **Least Privilege**: Acceso mínimo necesario por defecto
- **Defense in Depth**: Múltiples capas de seguridad
- **Audit Everything**: Logging completo de acciones sensibles
- **Fail Secure**: Fallos seguros que niegan acceso por defecto

### 1.2 Capas de Seguridad
1. **Network Security**: Firewalls, VPN, network segmentation
2. **Application Security**: Authentication, authorization, input validation
3. **Data Security**: Encryption, access controls, data classification
4. **Infrastructure Security**: Container security, secrets management
5. **Operational Security**: Monitoring, incident response, compliance

---

## 2. Autenticación

### 2.1 Métodos de Autenticación

#### Autenticación Primaria
- **Username/Password**: bcrypt hashing con salt único
- **Email Verification**: Obligatorio para activación de cuenta
- **Password Policy**: Mínimo 8 caracteres, complejidad configurable

#### Multi-Factor Authentication (MFA)
- **TOTP**: Time-based One-Time Password (RFC 6238)
- **Recovery Codes**: 5 códigos de un solo uso
- **Enforcement**: Configurable por tenant y rol

#### Single Sign-On (SSO)
- **SAML 2.0**: Para integraciones empresariales
- **OpenID Connect**: OAuth 2.0 based authentication
- **Provider Support**: Azure AD, Google Workspace, Okta

### 2.2 Gestión de Sesiones

#### JWT Tokens
```json
{
  "sub": "user_id",
  "tenant_id": "tenant_uuid",
  "roles": ["content_creator", "admin_tenant"],
  "permissions": ["course:create", "exam:publish"],
  "exp": **********,
  "iat": **********,
  "jti": "token_id"
}
```

#### Token Management
- **Access Token**: 15 minutos de vida útil
- **Refresh Token**: 7 días, rotación automática
- **Revocation**: Lista negra para logout inmediato
- **Scope Limitation**: Permisos específicos por token

---

## 3. Modelo RBAC

### 3.1 Jerarquía de Roles

| Nivel | Rol | Alcance | Capacidades Principales |
|-------|-----|---------|------------------------|
| **Sistema** | Owner | Global | Gestión de tenants, backups, compliance |
| **Tenant** | Admin Tenant | Organización | Gestión de usuarios, roles, grupos, configuración |
| **Usuario** | Usuario | Contenido asignado | Ver contenido, crear cursos/tests según permisos |

### 3.2 Definición Detallada de Roles

#### Owner (Sistema)
```yaml
permissions:
  - tenant:create
  - tenant:delete
  - tenant:configure
  - backup:create
  - backup:restore
  - audit:view_all
  - compliance:manage
  - system:monitor
```

**Características**:
- Acceso cross-tenant limitado
- Operaciones de infraestructura
- Gestión de compliance y auditoría

#### Admin Tenant (Organización)
```yaml
permissions:
  - user:create
  - user:edit
  - user:deactivate
  - role:create
  - role:assign
  - group:create
  - group:manage
  - course:manage_all
  - exam:manage_all
  - billing:view
  - analytics:view_all
  - integration:configure
  - permission:assign
```

**Características**:
- Administración completa del tenant
- Gestión de usuarios, roles y grupos
- Asignación de permisos granulares
- Configuración de integraciones

#### Usuario (Flexible según permisos asignados)
```yaml
# Permisos base para todos los usuarios
base_permissions:
  - course:view_assigned
  - course:rate # like/dislike courses
  - course:report # report inappropriate content
  - exam:attempt_assigned
  - result:view_own
  - profile:edit_own
  - progress:view_own

# Permisos adicionales que puede asignar Admin Tenant
optional_permissions:
  - course:create
  - course:edit_own
  - course:edit_assigned # edit courses where user is collaborator
  - course:delete_own
  - course:publish
  - course:manage_collaborators # add/remove collaborators
  - course:view_administration # access course admin page
  - question:create
  - question:generate_ai
  - exam:create
  - exam:edit_own
  - exam:grade_assigned # grade exams in assigned courses
  - answer:score_manual
  - student:view_progress
  - report:generate
  - feedback:provide
  - analytics:view_own
  - group:participate
  - forum:post # create posts in course forums
  - forum:reply # reply to forum posts
  - forum:like # like posts and replies
  - forum:moderate # moderate forum content (delete inappropriate posts)
  - expert:review_courses # create expert reviews for courses
  - expert:highlight_posts # highlight important forum messages
```

**Características**:
- Permisos base para consumir contenido y valorar cursos
- Sistema de colaboración granular en cursos
- Permisos adicionales asignables por Admin Tenant
- Flexibilidad para crear contenido según necesidades
- Acceso a herramientas de IA si se otorga permiso
- Sistema de reportes para moderación de contenido
- Participación en foros de cursos con likes y replies

#### Experto (Rol especial asignado por Admin Tenant)
```yaml
# Permisos base de usuario más permisos especiales de experto
expert_permissions:
  - all_base_user_permissions
  - expert:review_courses # crear reviews verificados de cursos
  - expert:highlight_posts # destacar mensajes importantes en foros
  - forum:moderate # moderar contenido de foros en su área
  - course:expert_access # acceso especial a cursos en su área de expertise

# Restricciones por área de expertise
area_restrictions:
  - expert_reviews: only_in_assigned_areas
  - forum_highlighting: only_in_relevant_courses
  - moderation: limited_to_expertise_area
```

**Características**:
- Rol especial asignado por Admin Tenant en áreas específicas
- Puede crear reviews verificados con calificación de 5 niveles
- Privilegios especiales en foros (destacar mensajes, moderación)
- Identificación visual especial con badges de experto
- Reviews requieren verificación de Admin Tenant antes de publicación
- Limitado a actuar solo en su área de expertise asignada

### 3.3 Gestión de Permisos

#### Categorización de Permisos
```yaml
categories:
  authentication:
    - auth:login
    - auth:logout
    - auth:mfa_setup
    
  user_management:
    - user:create
    - user:edit
    - user:view
    - user:deactivate
    
  content_management:
    - course:create
    - course:edit
    - course:publish
    - course:archive
    
  assessment:
    - exam:create
    - exam:attempt
    - exam:score
    - exam:publish
    
  ai_services:
    - ai:generate_questions
    - ai:score_answers
    - ai:moderate_content
    
  analytics:
    - analytics:view_own
    - analytics:view_team
    - analytics:view_all
    
  administration:
    - tenant:configure
    - billing:manage
    - integration:setup
```

#### Permisos Temporales
```sql
CREATE TABLE temporary_permissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES users(user_id),
    permission_id UUID REFERENCES permissions(permission_id),
    granted_by UUID REFERENCES users(user_id),
    active_from TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    reason TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

---

## 4. Control de Acceso Multi-Tenant

### 4.1 Aislamiento de Datos

#### Row-Level Security (RLS)
```sql
-- Política de aislamiento por tenant
CREATE POLICY tenant_isolation ON users
    FOR ALL TO application_role
    USING (tenant_id = current_setting('app.current_tenant_id')::UUID);

-- Política para SysAdmin
CREATE POLICY sysadmin_access ON users
    FOR ALL TO sysadmin_role
    USING (true);
```

#### Context Setting
```python
# Establecer contexto de tenant por request
async def set_tenant_context(tenant_id: UUID):
    await database.execute(
        "SELECT set_config('app.current_tenant_id', $1, true)",
        str(tenant_id)
    )
```

### 4.2 Validación de Acceso

#### Middleware de Autorización
```python
@app.middleware("http")
async def authorization_middleware(request: Request, call_next):
    # 1. Extraer token JWT
    # 2. Validar firma y expiración
    # 3. Verificar permisos para endpoint
    # 4. Establecer contexto de tenant
    # 5. Proceder con request o denegar acceso
    pass
```

#### Decoradores de Permisos
```python
@require_permission("course:create")
async def create_course(course_data: CourseCreate):
    # Lógica de creación de curso
    pass

@require_role("admin_tenant")
async def manage_users():
    # Gestión de usuarios del tenant
    pass
```

---

## 5. Seguridad de Datos

### 5.1 Clasificación de Datos

| Clasificación | Ejemplos | Protección |
|---------------|----------|------------|
| **Público** | Documentación, marketing | Ninguna especial |
| **Interno** | Configuración, logs | Acceso autenticado |
| **Confidencial** | Datos de usuario, resultados | Encriptación, auditoría |
| **Restringido** | Passwords, tokens | Encriptación fuerte, HSM |

### 5.2 Encriptación

#### En Tránsito
- **TLS 1.3**: Todas las comunicaciones externas
- **mTLS**: Comunicación entre microservicios
- **Certificate Pinning**: Apps móviles

#### En Reposo
- **Database**: Transparent Data Encryption (TDE)
- **Object Storage**: AES-256 encryption
- **Secrets**: HashiCorp Vault o Azure Key Vault

#### A Nivel de Aplicación
```python
# Encriptación de campos sensibles
from cryptography.fernet import Fernet

class EncryptedField:
    def __init__(self, key: bytes):
        self.cipher = Fernet(key)
    
    def encrypt(self, value: str) -> str:
        return self.cipher.encrypt(value.encode()).decode()
    
    def decrypt(self, encrypted_value: str) -> str:
        return self.cipher.decrypt(encrypted_value.encode()).decode()
```

### 5.3 Gestión de Secretos

#### Rotación Automática
- **Database Passwords**: Rotación cada 90 días
- **API Keys**: Rotación cada 30 días
- **JWT Signing Keys**: Rotación cada 24 horas

#### Acceso a Secretos
```yaml
# Ejemplo de política de acceso
vault_policy:
  path "secret/arroyo/prod/*":
    capabilities: ["read"]
  path "secret/arroyo/prod/database":
    capabilities: ["read", "update"]
```

---

## 6. Auditoría y Compliance

### 6.1 Logging de Seguridad

#### Eventos Auditables
- Autenticación exitosa/fallida
- Cambios de permisos y roles
- Acceso a datos sensibles
- Operaciones administrativas
- Exportación de datos

#### Formato de Logs
```json
{
  "timestamp": "2024-01-15T10:30:00Z",
  "event_type": "authentication",
  "user_id": "uuid",
  "tenant_id": "uuid",
  "action": "login_success",
  "ip_address": "*************",
  "user_agent": "Mozilla/5.0...",
  "metadata": {
    "mfa_used": true,
    "session_id": "session_uuid"
  }
}
```

### 6.2 Compliance Frameworks

#### GDPR/CCPA
- **Data Subject Rights**: Acceso, rectificación, eliminación
- **Consent Management**: Tracking granular de consentimientos
- **Data Portability**: Exportación en formato estándar
- **Breach Notification**: Alertas automáticas < 72 horas

#### FERPA (Educational Records)
- **Directory Information**: Clasificación automática
- **Parent Consent**: Para menores de 18 años
- **Access Logging**: Auditoría de acceso a registros educativos
- **Retention Policies**: Eliminación automática según políticas

#### SOC 2
- **Security**: Controles de acceso y protección
- **Availability**: Monitoreo de uptime y performance
- **Processing Integrity**: Validación de datos y procesos
- **Confidentiality**: Protección de información sensible

---

## 7. Monitoreo y Detección

### 7.1 Security Information and Event Management (SIEM)

#### Alertas de Seguridad
- Múltiples intentos de login fallidos
- Acceso desde ubicaciones inusuales
- Escalación de privilegios
- Acceso a datos fuera de horario laboral
- Patrones de acceso anómalos

#### Métricas de Seguridad
```yaml
security_metrics:
  authentication:
    - failed_login_rate
    - mfa_adoption_rate
    - session_duration_avg
  
  authorization:
    - permission_denied_rate
    - privilege_escalation_attempts
    - role_assignment_frequency
  
  data_access:
    - sensitive_data_access_rate
    - data_export_frequency
    - cross_tenant_access_attempts
```

### 7.2 Incident Response

#### Playbooks Automatizados
1. **Compromiso de Cuenta**: Revocación inmediata de tokens
2. **Acceso No Autorizado**: Bloqueo temporal y notificación
3. **Data Breach**: Aislamiento y notificación a stakeholders
4. **Malware Detection**: Cuarentena y análisis forense

---

## 8. Implementación UX

### 8.1 Interfaz de Gestión de Roles

#### Estilo Discord
- **Categorías Expandibles**: Agrupación lógica de permisos
- **Búsqueda Rápida**: Filtrado en tiempo real
- **Bulk Operations**: Asignación masiva de permisos
- **Visual Indicators**: Estados claros (granted/denied/inherited)

#### Ejemplo de UI
```jsx
<PermissionCategory name="Content Management" expanded={true}>
  <Permission 
    name="course:create" 
    description="Create new courses"
    granted={true}
    inherited={false}
  />
  <Permission 
    name="course:publish" 
    description="Publish courses to students"
    granted={false}
    inherited={true}
    inheritedFrom="Content Creator"
  />
</PermissionCategory>
```

### 8.2 Experiencia de Usuario

#### Onboarding de Seguridad
- Setup guiado de MFA
- Explicación clara de permisos
- Verificación de email obligatoria
- Políticas de password visibles

#### Transparencia
- Logs de acceso visibles al usuario
- Notificaciones de cambios de permisos
- Explicación de decisiones de acceso denegado

---

## Conclusión

El modelo de seguridad de Arroyo University proporciona una base sólida para proteger datos sensibles educativos mientras mantiene usabilidad y flexibilidad. La combinación de RBAC granular, multi-tenancy seguro y compliance automático asegura que la plataforma pueda operar en entornos empresariales regulados manteniendo la confianza de usuarios y organizaciones.
