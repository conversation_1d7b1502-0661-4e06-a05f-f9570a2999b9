# Arroyo University – Plataforma de cursos y exámenes impulsados por IA

## 0. Pro<PERSON>ósito y visión

Crear una **solución SaaS multi‑tenant** para que las organizaciones diseñen, impartan y supervisen formación interna. La plataforma permite crear evaluaciones y cursos para cualquier materia, totalmente autogenerados y autocorregidos con LLMs y servicios de voz, pero la **visión a 12‑24 meses** incluye soporte multilingüe, analítica predictiva de talento y un marketplace comunitario de cursos.

> **Meta estratégica**: reducir el tiempo y coste de evaluación educativa en un **80 %** y aumentar la retención de talento al ofrecer rutas de aprendizaje inmediatas basadas en los resultados.

---

## 1. Alcance de la 1ª iteración

| Dominio           | Alcance v1                                                                                                                                                                                                        | Fuera de alcance v1                                |
| ----------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | -------------------------------------------------- |
| **Cursos**        | CRUD de cursos y módulos; publicación, archivado y versionado inicial.                                                                                                                                            | Certificaciones externas, SCORM import/export.     |
| **English Test**  | • *Writing* → preguntas abiertas vía IA.<br>• *Listening* → guion + TTS + opciones múltiples.<br>• *Speaking* → pregunta abierta con grabación de audio y STT opcional.<br>• Escalabilidad para 10 k candidatos concurrentes. | Traducción instantánea, proctoring avanzado.       |
| **Generación IA** | OpenAI LLM + Azure Speech (TTS & STT) + filtros moderación.                                                                                                                                                        | Modelos propietarios on‑prem.                      |
| **Roles**         | Multi‑tenant con RBAC editable (checklist estilo Discord).                                                                                                                                                        | ABAC completo, etiqueta de seguridad confidencial. |
| **Usuarios**      | Alta automática de “usuarios temporales” por invitación con caducidad configurable.                                                                                                                               | Gestión de freelances externos.                    |
| **Reportes**      | Resultados inmediatos al creador, exportación CSV y JSON.                                                                                                                                                         | Integración Power BI embebida.                     |

> **Criterio de éxito**: desplegar piloto con **2 tenants reales** y obtener **NPS ≥ 40** tras el primer mes.

---

## 2. Arquitectura de alto nivel

| Capa           | Componente          | Tech                                                                | Responsabilidad                                                        |
| -------------- | ------------------- | ------------------------------------------------------------------- | ---------------------------------------------------------------------- |
| UI             | **Front‑Web**       | React 18, Vite, Tailwind, React Query, i18next                      | SPA, auth, grabación y reproducción audio, dashboards, PWA offline.    |
| Lógica         | **Core API**        | Python 3.11, FastAPI, SQLModel, Alembic                             | CRUD, RBAC, multi‑tenant enforcement, pagos, notificaciones.           |
| IA             | **AI Service**      | Python 3.11, FastAPI async                                          | Orquestación prompts, moderación, scoring rubric, caching.             |
| Persistencia   | **DB**              | PostgreSQL 15 + citext, pg_net, pgvector                           | Datos transaccionales, embeddings semánticos, funciones notificadoras. |
| Mensajería     | **Queue**           | Redis 7 + Celery, (futuro) Kafka                                    | Jobs IA, mailing, webhooks, facturación asíncrona.                     |
| Archivos       | **Storage**         | Azure Blob + CDN front door                                         | Audios, PDF, media; presigned URLs.                                    |
| Observabilidad | **Telemetry Stack** | Prometheus, Grafana, Loki, Sentry, OpenTelemetry                    | Logs, métricas, trazas distribuidas, alerting.                         |
| Entorno        | **Infra**           | Docker, docker‑compose dev, AKS prod, Terraform IaC, GitHub Actions | Build, test, deploy, canary releases, blue‑green db migrations.        |

### 2.1 Puntos de extensibilidad

* API Gateway con rate‑limit por tenant.
* WebSocket hub (SignalR) para eventos tiempo real.
* Plugin system (futuro) basado en WebAssembly para chequeos adicionales.

---

## 3. Modelo de datos (núcleo + extensiones)

```sql
-- =============================================================
-- 1. Gestión Multi‑Tenant
-- =============================================================

CREATE TABLE IF NOT EXISTS Tenants (
    tenant_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    plan VARCHAR(30) DEFAULT 'free',
    ia_tokens_used BIGINT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP
);

CREATE TABLE IF NOT EXISTS GeneralConfigurations (
    config_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES Tenants(tenant_id),
    config_key VARCHAR(100) NOT NULL,
    config_value TEXT NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =============================================================
-- 2. Usuarios, Roles y RBAC
-- =============================================================

CREATE TABLE IF NOT EXISTS Users (
    user_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES Tenants(tenant_id),
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    verified BOOLEAN DEFAULT FALSE,
    mfa_secret VARCHAR(64),
    last_login TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS Roles (
    role_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES Tenants(tenant_id),
    name VARCHAR(50) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS Permissions (
    permission_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS Groups (
    group_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES Tenants(tenant_id),
    name VARCHAR(100) NOT NULL,
    description TEXT
);

-- Puentes usuario‑rol, rol‑permiso, grupo‑rol, etc.
CREATE TABLE IF NOT EXISTS LinkRoles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES Users(user_id) ON DELETE CASCADE,
    role_id UUID REFERENCES Roles(role_id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS LinkPermissionsRoles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    role_id UUID REFERENCES Roles(role_id) ON DELETE CASCADE,
    permission_id UUID REFERENCES Permissions(permission_id)
);

CREATE TABLE IF NOT EXISTS GroupRoles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    group_id UUID REFERENCES Groups(group_id) ON DELETE CASCADE,
    role_id UUID REFERENCES Roles(role_id)
);

CREATE TABLE IF NOT EXISTS LinkUsersGroups (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES Users(user_id) ON DELETE CASCADE,
    group_id UUID REFERENCES Groups(group_id)
);

CREATE TABLE IF NOT EXISTS GroupLinkPermissionsRoles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    group_role_id UUID REFERENCES GroupRoles(id) ON DELETE CASCADE,
    permission_id UUID REFERENCES Permissions(permission_id),
    active_from TIMESTAMP,
    expires_at TIMESTAMP
);

-- =============================================================
-- 3. Contenido Educativo
-- =============================================================

CREATE TABLE IF NOT EXISTS Courses (
    course_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES Tenants(tenant_id),
    title VARCHAR(150) NOT NULL,
    description TEXT,
    is_published BOOLEAN DEFAULT FALSE,
    is_archived  BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS CoursesItems (
    item_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    course_id UUID REFERENCES Courses(course_id) ON DELETE CASCADE,
    storage_url TEXT NOT NULL,
    content_type VARCHAR(50),
    checksum CHAR(64),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =============================================================
-- 4. Banco de Preguntas (TestItems + sub‑tipos)
-- =============================================================

CREATE TABLE IF NOT EXISTS TestItems (
    test_item_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES Tenants(tenant_id),
    item_type VARCHAR(20) NOT NULL DEFAULT 'WRITING', -- WRITING|LISTENING|SPEAKING|CODE|VIDEO|IMAGE
    prompt TEXT NOT NULL,                 -- texto base de la pregunta
    content_type VARCHAR(50),             -- "text/plain", "audio/mp3", etc.
    metadata JSONB,                       -- {script, audio_url, options:[{id,text,is_correct},…], …}
    version INT DEFAULT 1,
    is_archived BOOLEAN DEFAULT FALSE,
    ai_prompt TEXT,                       -- prompt usado para generar
    llm_model VARCHAR(50),
    difficulty VARCHAR(10),
    status VARCHAR(20) DEFAULT 'READY',   -- READY|PENDING|FLAGGED
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT chk_item_type_valid CHECK (item_type IN ('WRITING','LISTENING','SPEAKING','CODE','VIDEO','IMAGE'))
);


CREATE TABLE IF NOT EXISTS TestItemTags (
    tag_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    test_item_id UUID REFERENCES TestItems(test_item_id) ON DELETE CASCADE,
    tag VARCHAR(50)
);

-- =============================================================
-- 5. Exámenes y ejecución
-- =============================================================

CREATE TABLE IF NOT EXISTS CoursesExams (
    exam_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    course_id UUID REFERENCES Courses(course_id) ON DELETE CASCADE,
    title VARCHAR(150),
    description TEXT,
    time_limit_sec INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS CoursesExamsItems (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    exam_id UUID REFERENCES CoursesExams(exam_id) ON DELETE CASCADE,
    test_item_id UUID REFERENCES TestItems(test_item_id)
);

CREATE TABLE IF NOT EXISTS ExamAttempts (
  attempt_id   UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  exam_id      UUID REFERENCES CoursesExams(exam_id) ON DELETE CASCADE,
  user_id      UUID REFERENCES Users(user_id) ON DELETE CASCADE,
  status       VARCHAR(20) DEFAULT 'IN_PROGRESS', -- FINISHED|TIMEOUT|ABANDONED
  started_at   TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  finished_at  TIMESTAMP,
  time_spent_sec INT,
  total_score  DECIMAL(5,2),
  cefr_level   VARCHAR(10),
  source_ip    INET,
  user_agent   TEXT,
  created_by   UUID,
  updated_by   UUID,
  updated_at   TIMESTAMP
);

CREATE TABLE IF NOT EXISTS ExamAnswers (
  answer_id       UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  attempt_id      UUID REFERENCES ExamAttempts(attempt_id) ON DELETE CASCADE,
  test_item_id    UUID REFERENCES TestItems(test_item_id),
  answer_type     VARCHAR(20) NOT NULL DEFAULT 'GENERIC', -- TEXT|MCQ|AUDIO|CODE|GENERIC
  answer_data     JSONB NOT NULL,  -- estructura libre según item_type
  ai_score        DECIMAL(4,2),
  manual_score    DECIMAL(4,2),
  rubric_json     JSONB,
  plagiarism_pct  DECIMAL(4,2),
  flagged         BOOLEAN DEFAULT FALSE,
  created_at      TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =============================================================
-- 6. Seguimiento, Métricas y Ops
-- =============================================================

CREATE TABLE IF NOT EXISTS StudentTranscripts (
    transcript_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES Users(user_id) ON DELETE CASCADE,
    course_id UUID REFERENCES Courses(course_id) ON DELETE CASCADE,
    score DECIMAL(5,2),
    progress_percentage DECIMAL(5,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS Notifications (
    notification_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES Users(user_id) ON DELETE CASCADE,
    message TEXT NOT NULL,
    sent_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    read BOOLEAN DEFAULT FALSE
);

-- Métricas base
CREATE TABLE IF NOT EXISTS Metrics (
    metric_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES Tenants(tenant_id),
    metric_name VARCHAR(100),
    metric_value DECIMAL(10,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS MetricsTags (
    tag_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    metric_id UUID REFERENCES Metrics(metric_id) ON DELETE CASCADE,
    kvp_tag VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS SystemAlerts (
    alert_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES Tenants(tenant_id),
    alert_message TEXT NOT NULL,
    severity VARCHAR(10) DEFAULT 'INFO',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Dashboards & Monitoring
CREATE TABLE IF NOT EXISTS Dashboards (
    dashboard_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES Tenants(tenant_id),
    name VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS MonitoringItems (
    item_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    dashboard_id UUID REFERENCES Dashboards(dashboard_id) ON DELETE CASCADE,
    item_name VARCHAR(100),
    item_value TEXT
);

-- Backups
CREATE TABLE IF NOT EXISTS Backups (
    backup_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES Tenants(tenant_id),
    backup_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    backup_location TEXT,
    status VARCHAR(10) DEFAULT 'SUCCESS' -- SUCCESS|FAIL
);

-- Audit Logs
CREATE TABLE IF NOT EXISTS AuditLogs (
    log_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES Tenants(tenant_id),
    user_id UUID,
    entity VARCHAR(50),
    entity_id UUID,
    action VARCHAR(20),
    diff JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- IA Jobs
CREATE TABLE IF NOT EXISTS GenerationJobs (
    job_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES Tenants(tenant_id),
    job_type VARCHAR(30),
    payload JSONB,
    status VARCHAR(20) DEFAULT 'PENDING',
    error TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    finished_at TIMESTAMP
);

-- Invoices Stripe
CREATE TABLE IF NOT EXISTS TenantInvoices (
    invoice_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES Tenants(tenant_id),
    stripe_id VARCHAR(70) UNIQUE,
    amount_cents INT,
    currency CHAR(3) DEFAULT 'USD',
    invoice_status VARCHAR(20) DEFAULT 'DUE',
    pdf_url TEXT,
    issued_at TIMESTAMP,
    paid_at TIMESTAMP
);

-- Webhook logs
CREATE TABLE IF NOT EXISTS WebhookLogs (
    log_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES Tenants(tenant_id),
    endpoint VARCHAR(150) NOT NULL,          -- URL llamada
    payload JSONB NOT NULL,                  -- body enviado/recibido
    response_code INT,                       -- HTTP status devuelto
    attempt INT DEFAULT 1,                   -- reintento actual
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

```
---

## 4. Roles de usuario y RBAC

| Rol | Quien lo asigna | Capacidades clave | Ejemplos de permisos |
| --- | --------------- | ----------------- | -------------------- |
| **Usuario** | Admin Tenant | Ver contenido, crear cursos/tests según permisos asignados. | `course:view`, `exam:attempt`, `course:create?`, `question:generate?` |
| **Usuario regular** | Admin Tenant | Consumir cursos, comentar en foros, crear contenido si dispone de `content:create`. | `course:read`, `discussion:post`, `content:create?` |
| **Creador de contenido** | Admin Tenant | Generar preguntas IA, armar exámenes, publicar cursos. | `question:generate`, `exam:create`, `course:publish` |
| **Admin Tenant** | Owner | CRUD de roles, dashboards, cuotas IA, billing tenant. | `role:manage`, `billing:view`, `quota:update` |
| **Owner** | Sistema | Alta tenants, backups cross‑region, borrado seguro (GDPR). | `tenant:create`, `backup:restore`, `gdpr:erase` |

> **Nota UX**: la interfaz de permisos replica la experiencia de Discord: categorías plegables, búsqueda rápida y toggles bulk.

---

## 5. Requisitos funcionales detallados

Cada **Historia de Usuario (HU)** está acompañada por sus **Criterios de Aceptación (CA)**. Los CA son verificables e incluyen métricas, estados HTTP y/o efectos en la base de datos.

### 5.1 Gestión de tenants y cuotas

- **HU‑01 Alta/baja tenants y planes**  
  **CA1**: URLs en formato `{tenant}.arroyo.app` responden *200 OK* < 1 s tras creación.  
  **CA2**: la creación dispara pipeline Terraform y registra backup `tag="bootstrap"`.  
  **CA3**: la baja marca `Tenants.deleted_at` y elimina contenedor Blob.
- **HU‑02 Configuraciones globales**  
  **CA1**: `GET /config` devuelve merge global+tenant < 100 ms.  
  **CA2**: `PATCH /config` solo *Admin Tenant*; genera `AuditLogs`.
- **HU‑03 Auto‑bloqueo de cuota IA**  
  **CA1**: >90 % cuota crea `SystemAlert` *WARNING*.  
  **CA2**: 100 % → `/questions/generate` devuelve `429 Quota Exceeded`.

### 5.2 Usuarios & autenticación

- **HU‑04 CRUD usuarios y verificación email**  
  **CA1**: `POST /users` envía email con token expira 24 h.  
  **CA2**: solo `Users.verified=TRUE` pueden iniciar sesión.
- **HU‑05 Habilitar MFA**  
  **CA1**: flujo TOTP (RFC 6238) + 5 códigos de recuperación.  
  **CA2**: si `enforce_mfa`, redirige a setup obligatorio.
- **HU‑06 SSO empresarial**  
  **CA1**: soporta SAML 2.0 & OIDC; metadata en `TenantSSOConfigs`.  
  **CA2**: mapea groups IdP → `Roles`.
- **HU‑07 Alta masiva CSV**  
  **CA1**: 10 k filas procesadas < 3 min (Celery).  
  **CA2**: filas con error generan CSV de rebote 48 h.

### 5.3 RBAC editable

- **HU‑08 Crear roles y checklist**  
  **CA1**: UI lista 100 % permisos de `Permissions`.  
  **CA2**: `POST /roles` inicia `version=1`.
- **HU‑09 Clonar roles**  
  **CA1**: `/roles/{id}/clone` crea copia “(Copy)”.  
  **CA2**: permisos clonados visibles inmediatamente.
- **HU‑10 Asignar roles**  
  **CA1**: cache RBAC refresca < 10 s.  
  **CA2**: notificación push al destinatario.
- **HU‑11 Permisos temporales**  
  **CA1**: valida `active_from` / `expires_at` ≥ 1 min.  
  **CA2**: cron revoca expirados cada hora.
- **HU‑12 Auditoría RBAC**  
  **CA1**: diffs JSON en `AuditLogs`.  
  **CA2**: CSV exportable por SysAdmin.

### 5.4 Cursos

- **HU‑13 CRUD cursos**  
  **CA1**: `POST /courses` crea estado *DRAFT*.  
  **CA2**: borrado lógico via `is_archived`.
- **HU‑14 Contenidos multimedia**  
  **CA1**: ≤ 200 MB, MIME válido o `415`.  
  **CA2**: checksum `sha256` guardado.
- **HU‑15 Clonar curso**  
  **CA1**: `/courses/{id}/clone` duplica y agrega “(Copy)”.  
  **CA2**: mantiene `tenant_id`.
- **HU‑16 Prerrequisitos y cierre**  
  **CA1**: `closing_at` > `NOW()`; auto‑close.  
  **CA2**: prereq faltante → `409 Conflict`.

### 5.5 Banco de preguntas & IA

- **HU‑17 Crear/editar preguntas manuales**  
  **CA1**: Markdown + imágenes preview.  
  **CA2**: editar versión publicada crea nueva `version`.
- **HU‑18 Generar lote IA**  
  **CA1**: prompt incluye dificultad, tema, tono; latencia < 6 s/5 preg.
  **CA2**: `status=READY` + WebSocket notif.
- **HU‑19 Importar CSV/QTI**  
  **CA1**: errores indican línea/columna.  
  **CA2**: archivo original guardado.
- **HU‑20 Versionado/archivado**  
  **CA1**: `is_archived` oculta en picker pero conserva FKs.  
  **CA2**: solo última versión editable.
- **HU‑21 Moderación automática**  
  **CA1**: contenido ofensivo → `flagged=TRUE`.  
  **CA2**: digest diario para Admin Tenant.

### 5.6 Exámenes

- **HU‑22 Crear examen**  
  **CA1**: estado *DRAFT*; máx 1 placement por curso.  
  **CA2**: `time_limit_sec` ≤ 10 000.
- **HU‑23 Seleccionar/generar preguntas**  
  **CA1**: búsqueda full‑text pg_trgm.  
  **CA2**: mínimo 1 pregunta por skill.
- **HU‑24 Configurar temporizador**  
  **CA1**: cuenta atrás global o por pregunta.  
  **CA2**: expirado → status *TIMEOUT*.
- **HU‑25 Preguntas condicionales (stretch)**  
  **CA1**: columna `branch_logic JSONB` preparada.  
  **CA2**: UI oculta config hasta feature flag.
- **HU‑26 PDF imprimible**  
  **CA1**: `/export?format=pdf` + firma digital.  
  **CA2**: PDF retenido 30 días.

### 5.7 Presentación del examen

- **HU‑27 Intento y reanudación**  
  **CA1**: único `IN_PROGRESS` por usuario.  
  **CA2**: desconexión > 30 min → *ABANDONED*.
- **HU‑28 Autosave writing**  
  **CA1**: patch cada 10 s; pérdida ≤ 20 chars.  
  **CA2**: UI "guardado" con `204`.
- **HU‑29 Listening audio**  
  **CA1**: HLS adaptativo, fallback MP3.  
  **CA2**: botón repetir 1 vez si permitido.
- **HU‑30 Speaking grabación**  
  **CA1**: WebRTC Opus 48 kHz; máx 90 s.  
  **CA2**: preview con re‑grabación única.
- **HU‑31 Offline PWA**  
  **CA1**: IndexedDB ≤ 50 MB, LRU purge.  
  **CA2**: sync al recuperar conexión.
- **HU‑32 Accesibilidad**  
  **CA1**: atajos N/P.  
  **CA2**: contraste WCAG AA.

### 5.8 Corrección y scoring

- **HU‑33 Auto‑score MCQ**  
  **CA1**: correct = `ai_score=1`.  
  **CA2**: sumatoria simple.
- **HU‑34 Scoring IA**  
  **CA1**: GPT‑4 rúbrica 0‑5 < 4 s.  
  **CA2**: `rubric_json` campos `coherence`,`grammar`,`pronunciation`.
- **HU‑35 Plagio**  
  **CA1**: Turnitin < 15 % pasa.  
  **CA2**: > 40 % → `flagged=TRUE`.
- **HU‑36 Override manual**  
  **CA1**: evaluador edita `ai_score`; guarda `updated_by`.  
  **CA2**: notificación instantánea candidato.

### 5.9 Analítica & reportes

- **HU‑37 Dashboards**  
  **CA1**: filtro equipo + fechas.  
  **CA2**: carga < 2 s (10 k).
- **HU‑38 Export CSV/PDF**  
  **CA1**: UTF‑8, separador configurable.  
  **CA2**: enlace expira 24 h.
- **HU‑39 Webhooks**  
  **CA1**: JSON API 1.0 + HMAC.  
  **CA2**: 5 reintentos exponencial.
- **HU‑40 Analítica predictiva**  
  **CA1**: modelo recalibra semanal.  
  **CA2**: AUC ≥ 75 %.

### 5.10 Notificaciones & alertas

- **HU‑41 Fin corrección**  
  **CA1**: email+push ≤ 2 min.  
  **CA2**: link feedback.
- **HU‑42 Cuota IA**  
  **CA1**: 80/90/100 % INFO/WARN/CRIT.  
  **CA2**: Slack+email.
- **HU‑43 Recordatorio expiración**  
  **CA1**: 24 h y 1 h antes si no finalizó.  
  **CA2**: no duplica si ya terminó.

### 5.11 Backups y restauración

- **HU‑44 Backup nightly**  
  **CA1**: gzip + checksum; retención 30 días.  
  **CA2**: `Backups.status` SUCCESS/FAIL.
- **HU‑45 Restore selectivo**  
  **CA1**: `dry_run` lista impacto.  
  **CA2**: solo SysAdmin; log audit.

### 5.12 Suscripción y facturación

- **HU‑46 Upgrade/downgrade plan**  
  **CA1**: Stripe prorratea y créditos.  
  **CA2**: actualiza `Tenants.plan` y muestra banner < 10 s.  
  **CA3**: downgrade bajo consumo actual → `SystemAlert` grace 24 h.
- **HU‑47 Facturas automáticas**  
  **CA1**: `invoice.paid` → `TenantInvoices.PAID` + PDF mail.  
  **CA2**: tres reintentos pago fallido; luego *grace_period* 7 d.  
  **CA3**: PDF retenido 7 años.
- **HU‑48 Congelación por impago**  
  **CA1**: bloquea IA + invitaciones; intentos activos siguen.  
  **CA2**: desbloqueo automático tras pago.

### 5.13 Integraciones externas

- **HU‑49 Configurar SSO**  
  **CA1**: wizard valida `metadata.xml` + test login < 30 s.  
  **CA2**: IdP caído → fallback solo SysAdmin.
- **HU‑50 Webhooks HRIS/LMS**  
  **CA1**: firmas HMAC; secreto por tenant.  
  **CA2**: retry y `WebhookLogs` errores.
- **HU‑51 API pública**  
  **CA1**: OAuth2 scopes específicos.  
  **CA2**: rate‑limit 1000 RPM; `429` si excede.


---

## 6. Flujos clave

Cada sub‑sección ahora incluye **dos tablas complementarias**:
1. **Vista back‑end / DevOps** – cómo se orquestan microservicios y colas.  
2. **Vista front‑end / usuario** – pasos e interacciones visibles en la UI.

### 6.1 Alta de tenant y aprovisionamiento

**Back‑end / DevOps**

| # | Actor / Servicio | Acción | SLA |
|---|------------------|--------|-----|
| 1 | SysAdmin | `POST /tenants` → 201 | ≤ 300 ms |
| 2 | Kafka | Publica `tenant.created` | ≤ 50 ms |
| 3 | Provisioner | Terraform apply (AKS ns, Blob) | ≤ 90 s |
| 4 | Worker (Mail) | `202 Accepted` – Email bienvenida | ≤ 2 s |

**Front‑end / Usuario**

| Paso | Interfaz | Acción del usuario | Resultado / Feedback |
|------|----------|-------------------|----------------------|
| A | Portal SysAdmin | Completa formulario “Create tenant” | Toast “Tenant ready” < 5 s |
| B | Email Admin Tenant | Click en enlace de onboarding | Redirección a **Set Password** |
| C | Wizard onboarding | Configura nombre, logo, dominio | Acceso al dashboard inicial |

---

### 6.2 Generación de preguntas IA

El **Creador de contenido**:
1. Selecciona el **tipo** de pregunta a generar:  
   • *Writing* (abierta de escritura)  
   • *Listening* (conversación + MCQ)  
   • *Speaking* (pregunta abierta con grabación)  
2. Introduce un **prompt o tema** (ej.: *“Hobbies y tiempo libre”*).
3. Define el **número de preguntas** (N).  
4. Pulsa **Generar**.

*Caso Listening*: primero se genera un **script de conversación corta** (≈ 20‑30 s de audio, dos interlocutores A & B). Luego el sistema produce **N preguntas de opción múltiple** sobre el script y sintetiza el audio con Azure TTS.

#### Back‑end / DevOps

| # | Actor/Servicio | Acción | SLA |
|---|----------------|--------|-----|
| 1 | Content Creator | `POST /questions/generate` payload `{type, prompt, num}` | ≤ 200 ms |
| 2 | Core API | Inserta `GenerationJob` (status=PENDING) | — |
| 3 | Celery worker | *Branch logic*:<br>• **Listening** → Llama LLM para *script conversación* (2 speakers, ~150 palabras) | ≤ 8 s |
| 4 | Celery worker | **Listening** → Llama LLM para *N MCQ* basadas en script | ≤ 7 s |
| 5 | Celery worker | **Writing/Speaking** → LLM genera *N prompts* directos | ≤ 10 s |
| 6 | Celery worker | Si `type=listening` → Azure TTS convierte script a audio MP3 | ≤ 5 s |
| 7 | Celery worker | Graba `TestItems`, `TestItemOptions`, assets (audio URL) | — |
| 8 | Core API | Actualiza `GenerationJob` a *COMPLETED* & WebSocket notif | ≤ 500 ms |

#### Front‑end / Usuario

| Paso | Pantalla | Acción del usuario | Feedback UI |
|------|---------|-------------------|-------------|
| A | **Generador IA** | Selecciona tipo (*Writing / Listening / Speaking*) | Radio buttons con iconos |
| B | Generador IA | Escribe **prompt/tema** | Placeholder “Describe topic…” |
| C | Generador IA | Elige **N preguntas** (slider 1‑20) | Contador dinámico |
| D | Generador IA | Click **Generar** | Spinner + barra progreso (0‑100 %) |
| E | Si *Listening* | Estado “1/2: Creando conversación…” → “2/2: Creando preguntas…” | Chips de estado paso‑a‑paso |
| F | Resultado | Lista tarjetas pregunta +<br>• Preview script + botón ► (audio) (*Listening*)<br>• Prompt abierto (*Writing/Speaking*) | Badge “AI” + botón **Revisar/Editar** |


---

### 6.3 Creación y publicación de examen

**Back‑end / DevOps**

| # | Servicio | Acción | SLA |
|---|----------|--------|-----|
| 1 | Core API | `POST /exams` → `CoursesExams` (DRAFT) | ≤ 200 ms |
| 2 | Core API | `PUT /exams/{id}/items` – agrega ítems | ≤ 300 ms |
| 3 | Core API | `PUT /exams/{id}` `is_published=true` | ≤ 200 ms |
| 4 | Kafka | Evento `exam.published` | — |

**Front‑end / Usuario**

| Paso | Pantalla | Acción | Feedback |
|------|---------|--------|----------|
| A | Builder examen | Drag‑and‑drop preguntas | Orden se guarda autosave |
| B | Builder examen | Pulsa **Publicar** | Diálogo confirmación |
| C | Dashboard exámenes | Card pasa a estado *Publicado* | Etiqueta verde “LIVE” |

---

### 6.4 Intento de examen y entrega

**Back‑end / DevOps**

| # | Actor | Acción | SLA |
|---|-------|--------|-----|
| 1 | Candidato | `POST /exam-attempts` (token) | ≤ 150 ms |
| 2 | Core API | Registra `ExamAttempts` *(IN_PROGRESS)* | — |
| 3 | UI / API | `GET /attempts/{id}/next` | ≤ 100 ms |
| 4 | Candidato | `POST /answers` (autosave) | ≤ 150 ms |
| 5 | Candidato | `POST /attempts/{id}/finish` | — |
| 6 | Core API | Auto‑score MCQ | ≤ 300 ms |
| 7 | Celery | Job scoring IA | ≤ 4 s por respuesta |
| 8 | Core API | Calcula `total_score` | — |
| 9 | Worker Mail | Notifica resultado | ≤ 2 min |

**Front‑end / Usuario**

| Paso | Pantalla | Acción | Feedback |
|------|---------|--------|----------|
| A | Landing examen | Clic **Iniciar** | Temporizador global comienza |
| B | Writing Q | Escribe respuesta | Indicador autosave ✓ |
| C | Listening Q | Reproduce audio | Repetir (1) disponible |
| D | Speaking Q | Graba audio | VU‑meter + pre‑escucha |
| E | Resumen | Pulsa **Enviar examen** | Modal confirmación |
| F | Pantalla fin | Mensaje “Resultados en breve” | Loading bar progreso IA |

---

### 6.5 Corrección manual (override)

**Back‑end / DevOps**

| # | Servicio | Acción | SLA |
|---|----------|--------|-----|
| 1 | Evaluador | `GET /attempts/{id}` | ≤ 200 ms |
| 2 | Evaluador | `PATCH /answers/{id}` (`ai_score`) | ≤ 150 ms |
| 3 | Core API | Re‑calcula `total_score` | — |
| 4 | Worker | Envía notificación override | ≤ 1 min |

**Front‑end / Usuario**

| Paso | Pantalla | Acción | Feedback |
|------|---------|--------|----------|
| A | Panel revisión | Edita puntuación slider | Campo cambia a amarillo “Overridden” |
| B | Panel revisión | Pulsa **Guardar** | Toast “Cambios aplicados” |
| C | Candidato | Recibe email actualización | Link a feedback actualizado |

---

### 6.6 Pipeline de dashboards y métricas

**Back‑end / DevOps**

| # | Servicio | Acción | SLA |
|---|----------|--------|-----|
| 1 | Cron Job | Agrega métricas a `Metrics` | Cada 5 min |
| 2 | Materialized View | Refresca KPI tablas | ≤ 30 s |
| 3 | Grafana | Consulta Prometheus | — |

**Front‑end / Usuario**

| Paso | Pantalla | Acción | Feedback |
|------|---------|--------|----------|
| A | Dashboard Admin | Selecciona rango fechas | Gráfico se refresca < 1 s |
| B | Dashboard Admin | Clic descarga CSV | Archivo inicia descarga |

---

### 6.7 Upgrade/downgrade de plan de suscripción

**Back‑end / DevOps**

| # | Servicio | Acción | SLA |
|---|----------|--------|-----|
| 1 | Admin Tenant | Click **Upgrade plan** (Stripe) | — |
| 2 | Stripe | `checkout.session.completed` webhook | < 3 s |
| 3 | Core API | Actualiza `Tenants.plan` | ≤ 200 ms |
| 4 | Worker | Envía factura PDF | ≤ 1 min |

**Front‑end / Usuario**

| Paso | Pantalla | Acción | Feedback |
|------|---------|--------|----------|
| A | Billing page | Selecciona nuevo plan | Redirección Checkout |
| B | Checkout | Completa pago | Spinner + redirect back |
| C | Billing page | Banner “Plan actualizado” | Nuevo límite IA visible |

---

### 6.8 Backup y restore

**Back‑end / DevOps**

| # | Actor | Acción | SLA |
|---|------|--------|-----|
| 1 | Cron Job | Ejecuta `pg_dump` + gzip | 02:00 UTC |
| 2 | Script | Verifica checksum | +5 min |
| 3 | Core API | Inserta fila `Backups` | — |
| 4 | SysAdmin | Dispara restore (manual) | Variable |

**Front‑end / Usuario**

| Paso | Pantalla | Acción | Feedback |
|------|---------|--------|----------|
| A | Console SysAdmin | Ve lista Backups | Badge OK/FAIL |
| B | Backup detail | Click **Restore** | Modal confirmación |
| C | Console SysAdmin | Progreso restore 0‑100 % | Banner éxito/fracaso |

---

### 6.9 Flujos mobile/PWA offline

**Back‑end / DevOps**

| # | Servicio | Acción | SLA |
|---|----------|--------|-----|
| 1 | Service Worker | Cachea `/static/*` | Primer load |
| 2 | IndexedDB | Guarda respuestas offline | Instantáneo |
| 3 | Sync Manager | Re‑envía lote respuestas | < 5 s reconexión |

**Front‑end / Usuario**

| Paso | Pantalla | Acción | Feedback |
|------|---------|--------|----------|
| A | Examen móvil offline | Continúa respondiendo | Banner “Modo offline” |
| B | Reconexión | N/A | Banner cambia a “Sincronizando…” |
| C | Sincronizado | N/A | Toast verde “Respuestas enviadas” |

---

## 7. Requisitos no funcionales

| Categoría | Objetivo / SLA | Métrica verificable | Herramientas / Prácticas |
|-----------|----------------|---------------------|--------------------------|
| **Seguridad** | Cumplir OWASP Top‑10 y CIS Benchmarks. | Pentest trimestral, 0 CVE críticas > 30 días. | OWASP ZAP, Snyk, Dependabot, secret‑scanning. |
| **Rendimiento** | p95 < 200 ms CRUD; generación IA end‑to‑end < 15 s. | APM traces, pruebas k6. | OpenTelemetry, Grafana SLO. |
| **Disponibilidad** | SLO 99.5 % mensual para API pública. | Tiempo caído < 3 h/mes. | AKS zona‑redundante, sla monitors. |
| **Escalabilidad** | 10 k usuarios concurrentes sin degradación. | CPU < 70 %, latencia < 300 ms bajo carga. | HPA AKS, Redis Cluster, Celery autoscale. |
| **Coste IA** | Coste < 0,30 USD por test completo. | Dashboard coste diario por tenant. | Azure Cost Insights + etiquetado. |
| **Accesibilidad** | Cumplir WCAG 2.1 AA. | Lighthouse a11y ≥ 90. | Playwright a11y tests, QA manual. |
| **Mantenibilidad** | Cobertura ≥ 80 % unit tests. | SonarQube rating ≥ A. | pytest, coverage, SonarCloud, ADR docs. |
| **Internacionalización** | UI es/en; 100 % strings externalizadas. | Lint i18n sin claves huérfanas. | i18next, extractor CI. |
| **Cumplimiento** | GDPR/CCPA ready, auditoría 7 años. | DSR resuelto ≤ 30 d. | AuditLogs, DSR API, cifrado at‑rest. |
| **Observabilidad** | Logs JSON, trazas 100 %. | MTTR < 30 min. | Prometheus, Loki, Sentry alerts. |
| **Resiliencia** | Auto‑retry + circuit breakers. | Error rate < 0.1 %. | Polly libs, pod disruption budget. |
| **UX Performance** | LCP < 2.5 s, CLS < 0.1. | Web Vitals en prod. | React lazy, img opt. |
| **Compatibilidad** | Navegadores evergreen + último Safari iOS. | Cobertura > 98 % usuarios. | Browserstack CI matrix. |
| **Disaster Recovery** | RPO 24 h, RTO 4 h. | Simulacro DR semestral. | Backups cross‑region, Terraform. |
| **Calidad código** | Cumplir PEP 8 / ESLint strict. | CI lint sin errores. | pre‑commit, linters. |


## 8. Catálogo de endpoints (v1)

> **Nota**: todos los endpoints siguen el prefijo REST `/v1` y devuelven JSON con formato API Problem para errores.  
> Columnas **Auth**: 🔒 = requiere token JWT; 🔑 = firma HMAC (webhook); 🌐 = público.

### 8.1 Core API – Servicios transaccionales

| Método | URI | Descripción | Auth |
| ------ | --- | ----------- | ---- |
| **POST** | `/auth/login` | Login usuario + refresh token | 🌐 |
| **POST** | `/auth/refresh` | Renovar access token | 🌐 |
| **GET** | `/config` | Config global + tenant merge | 🔒 |
| **PATCH** | `/config` | Actualizar configuración | 🔒 |
| **POST** | `/tenants` | Crear nuevo tenant | 🔒 *(SysAdmin)* |
| **GET** | `/tenants/{id}` | Detalle tenant | 🔒 |
| **DELETE** | `/tenants/{id}` | Baja lógica tenant | 🔒 *(SysAdmin)* |
| **POST** | `/users` | Crear usuario / invitación | 🔒 |
| **GET** | `/users/{id}` | Perfil usuario | 🔒 |
| **PATCH** | `/users/{id}` | Editar usuario | 🔒 |
| **POST** | `/roles` | Crear rol | 🔒 |
| **GET** | `/roles` | Listar roles | 🔒 |
| **POST** | `/permissions` | Crear permiso | 🔒 *(SysAdmin)* |
| **GET** | `/permissions` | Listar permisos | 🔒 |
| **POST** | `/groups` | Crear grupo/equipo | 🔒 |
| **POST** | `/courses` | Crear curso | 🔒 |
| **GET** | `/courses/{id}` | Ver curso | 🔒 |
| **PATCH** | `/courses/{id}` | Publicar/archivar curso | 🔒 |
| **POST** | `/courses/{id}/items` | Añadir contenido multimedia | 🔒 |
| **POST** | `/questions` | Crear pregunta manual | 🔒 |
| **GET** | `/questions` | Buscar preguntas | 🔒 |
| **POST** | `/questions/generate` | Crear lote IA (delegado al AI Service) | 🔒 |
| **POST** | `/exams` | Crear examen | 🔒 |
| **PUT** | `/exams/{id}/items` | Asignar preguntas al examen | 🔒 |
| **PATCH** | `/exams/{id}` | Publicar examen | 🔒 |
| **POST** | `/exam-attempts` | Iniciar intento | 🌐 *(temporal)* |
| **GET** | `/attempts/{id}/next` | Obtener próxima pregunta | 🔒 |
| **POST** | `/answers` | Enviar respuesta | 🔒 |
| **POST** | `/attempts/{id}/finish` | Finalizar intento | 🔒 |
| **GET** | `/reports/exams/{id}` | Resultados examen | 🔒 |
| **GET** | `/metrics` | KPIs y uso cuota IA | 🔒 |
| **POST** | `/billing/stripe/webhook` | Webhook Stripe | 🔑 |
| **GET** | `/backups` | Listar backups | 🔒 *(SysAdmin)* |
| **POST** | `/backups/{id}/restore` | Restore selectivo | 🔒 *(SysAdmin)* |

### 8.2 AI Service – Servicios IA & Audio

| Método | URI | Descripción | Auth |
| ------ | --- | ----------- | ---- |
| **POST** | `/ai/generate/writing` | Generar N prompts *writing* | 🔒 *(internal)* |
| **POST** | `/ai/generate/listening-script` | Generar script conversación | 🔒 |
| **POST** | `/ai/generate/listening-mcq` | Generar MCQ sobre script | 🔒 |
| **POST** | `/ai/generate/mcq-distractors` | Generar solo distractores para MCQ existentes | 🔒 |
| **POST** | `/ai/generate/speaking` | Generar prompts *speaking* | 🔒 |
| **POST** | `/ai/tts` | Sintetizar audio TTS (Azure) | 🔒 |
| **POST** | `/ai/stt` | Transcribir audio (Whisper/STT) | 🔒 |
| **POST** | `/ai/normalize-audio` | Normalizar/limpiar audio de entrada | 🔒 |
| **POST** | `/ai/score/writing` | Scoring IA de respuesta escrita | 🔒 |
| **POST** | `/ai/score/speaking` | Scoring IA de respuesta hablada | 🔒 |
| **POST** | `/ai/score/plagiarism` | Detección de similitud / plagio | 🔒 |
| **POST** | `/ai/moderate` | Clasificar contenido ofensivo | 🔒 |
| **GET** | `/ai/rubrics` | Listar rúbricas de scoring disponibles | 🔒 |
| **GET** | `/ai/jobs` | Listar jobs por tenant/estado | 🔒 |
| **GET** | `/ai/jobs/{id}` | Estado job generación/scoring | 🔒 |
| **POST** | `/ai/jobs/{id}/cancel` | Cancelar job en progreso | 🔒 |

> Todos los endpoints del **AI Service** están protegidos por mutual TLS y sólo son accesibles desde la VNet AKS; la Core API actúa como cliente.
