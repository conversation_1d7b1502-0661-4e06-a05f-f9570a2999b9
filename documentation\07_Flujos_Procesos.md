# Flujos y Procesos Clave - Arroyo University

## Introducción

Este documento describe los flujos de trabajo críticos de Arroyo University, proporcionando dos perspectivas complementarias para cada proceso: la vista técnica backend/DevOps y la experiencia de usuario frontend. Cada flujo incluye SLAs específicos y puntos de control de calidad.

---

## 1. Alta de Tenant y Aprovisionamiento

### 1.1 Descripción del Proceso
Proceso automatizado para crear nuevos tenants (organizaciones) en la plataforma, incluyendo aprovisionamiento de infraestructura, configuración inicial y onboarding del administrador.

### 1.2 Vista Backend/DevOps

| # | Actor/Servicio | Acción | SLA | Validaciones |
|---|----------------|--------|-----|--------------|
| 1 | SysAdmin | `POST /tenants` con datos básicos | ≤ 300ms | Validación de dominio único |
| 2 | Core API | Inserta registro en `Tenants` tabla | ≤ 100ms | Constraints de DB |
| 3 | Kafka | Publica evento `tenant.created` | ≤ 50ms | Confirmación de entrega |
| 4 | Provisioner Service | Terraform apply (AKS namespace, Blob container) | ≤ 90s | Verificación de recursos |
| 5 | DNS Service | Configura subdomain `{tenant}.arroyo.app` | ≤ 30s | Propagación DNS |
| 6 | Mail Worker | Envía email de bienvenida con setup link | ≤ 2s | Template validation |
| 7 | Backup Service | Crea backup inicial con tag "bootstrap" | ≤ 10s | Verificación de integridad |

### 1.3 Vista Frontend/Usuario

| Paso | Interfaz | Acción del Usuario | Resultado/Feedback | Tiempo Esperado |
|------|----------|-------------------|-------------------|-----------------|
| A | Portal SysAdmin | Completa formulario "Create Tenant" | Toast "Tenant ready" | < 5s |
| B | Email Admin Tenant | Click en enlace de onboarding | Redirección a setup wizard | Inmediato |
| C | Setup Wizard | Configura nombre, logo, dominio personalizado | Progress bar 0-100% | 5-10min |
| D | Setup Wizard | Crea primer usuario Admin Tenant | Confirmación por email | < 30s |
| E | Dashboard | Acceso al dashboard inicial con tour guiado | Onboarding interactivo | 2-5min |

### 1.4 Puntos de Control
- **Rollback automático** si falla aprovisionamiento de infraestructura
- **Verificación de DNS** antes de marcar tenant como activo
- **Validación de email** obligatoria para Admin Tenant
- **Backup de configuración** inicial para disaster recovery

---

## 2. Generación de Preguntas con IA

### 2.1 Descripción del Proceso
Flujo para generar preguntas educativas automáticamente usando IA, con diferentes tipos (Writing, Listening, Speaking) y procesamiento asíncrono para optimizar experiencia de usuario.

### 2.2 Vista Backend/DevOps

| # | Actor/Servicio | Acción | SLA | Recursos |
|---|----------------|--------|-----|----------|
| 1 | Content Creator | `POST /questions/generate` con payload `{type, prompt, count}` | ≤ 200ms | Validación de cuota IA |
| 2 | Core API | Inserta `GenerationJob` con status=PENDING | ≤ 50ms | DB transaction |
| 3 | Celery Worker | Procesa job según tipo de pregunta | Variable | CPU/Memory scaling |
| 3a | **Writing/Speaking** | LLM genera N prompts directos | ≤ 10s | OpenAI API |
| 3b | **Listening** | LLM genera script conversación (150 palabras) | ≤ 8s | OpenAI API |
| 3c | **Listening** | LLM genera N MCQ basadas en script | ≤ 7s | OpenAI API |
| 3d | **Listening** | Azure TTS convierte script a audio MP3 | ≤ 5s | Azure Speech API |
| 4 | Celery Worker | Almacena resultados en `TestItems` y `TestItemOptions` | ≤ 1s | Bulk insert |
| 5 | Core API | Actualiza job a COMPLETED + WebSocket notification | ≤ 500ms | Real-time update |

### 2.3 Vista Frontend/Usuario

| Paso | Pantalla | Acción del Usuario | Feedback UI | Estado |
|------|---------|-------------------|-------------|--------|
| A | Generador IA | Selecciona tipo (Writing/Listening/Speaking) | Radio buttons con iconos descriptivos | Input |
| B | Generador IA | Escribe prompt/tema específico | Placeholder "Ej: Hobbies y tiempo libre" | Input |
| C | Generador IA | Ajusta cantidad (slider 1-20) | Contador dinámico + estimación de tiempo | Input |
| D | Generador IA | Click "Generar" | Spinner + barra de progreso (0-100%) | Processing |
| E | **Si Listening** | Progreso por etapas | "1/3: Script... 2/3: Preguntas... 3/3: Audio..." | Processing |
| F | Resultado | Lista de preguntas generadas | Preview + botón ▶ (audio) + "Editar" | Complete |
| G | Resultado | Opción de regenerar o guardar | Badges "AI Generated" + metadata visible | Action |

### 2.4 Manejo de Errores
- **Timeout IA**: Retry automático hasta 3 veces
- **Cuota excedida**: Notificación con opción de upgrade
- **Contenido inapropiado**: Filtrado automático + regeneración
- **Fallo de TTS**: Fallback a texto sin audio

---

## 3. Intento de Examen y Entrega

### 3.1 Descripción del Proceso
Flujo completo desde el inicio de un examen hasta la entrega final, incluyendo autosave, manejo de desconexiones y scoring automático.

### 3.2 Vista Backend/DevOps

| # | Actor | Acción | SLA | Consideraciones |
|---|-------|--------|-----|-----------------|
| 1 | Candidato | `POST /exam-attempts` con token de invitación | ≤ 150ms | Validación de token y permisos |
| 2 | Core API | Crea registro `ExamAttempts` con status=IN_PROGRESS | ≤ 100ms | Único intento activo por usuario |
| 3 | Frontend | `GET /attempts/{id}/next` para obtener primera pregunta | ≤ 100ms | Randomización si configurada |
| 4 | Candidato | `POST /answers` cada 10s (autosave) | ≤ 150ms | Optimistic locking |
| 5 | Candidato | `POST /attempts/{id}/finish` al completar | ≤ 200ms | Validación de completitud |
| 6 | Core API | Auto-score preguntas MCQ inmediatamente | ≤ 300ms | Cálculo simple |
| 7 | Celery Worker | Job de scoring IA para respuestas abiertas | ≤ 4s/respuesta | Queue prioritaria |
| 8 | Core API | Calcula `total_score` y `cefr_level` | ≤ 500ms | Algoritmo de scoring |
| 9 | Mail Worker | Envía notificación de resultados | ≤ 2min | Template personalizado |

### 3.3 Vista Frontend/Usuario

| Paso | Pantalla | Acción del Usuario | Feedback Visual | Tiempo |
|------|---------|-------------------|-----------------|--------|
| A | Landing Examen | Lee instrucciones + click "Iniciar" | Temporizador global inicia | Setup |
| B | Pregunta Writing | Escribe respuesta en editor | Contador de palabras + autosave ✓ | Variable |
| C | Pregunta Listening | Reproduce audio + selecciona respuesta | Controles de audio + "Repetir (1)" | 2-5min |
| D | Pregunta Speaking | Graba respuesta de audio | VU-meter + preview + "Re-grabar (1)" | 1-3min |
| E | Navegación | Usa botones "Anterior/Siguiente" | Progress bar + pregunta N de M | Continuo |
| F | Resumen Final | Revisa respuestas + click "Enviar Examen" | Modal de confirmación crítico | Review |
| G | Finalización | Mensaje "Resultados en proceso" | Loading animation + tiempo estimado | Wait |
| H | Resultados | Recibe email + accede a feedback detallado | Score + breakdown por skill | Complete |

### 3.4 Características Especiales
- **Offline Support**: PWA cachea preguntas para continuidad
- **Proctoring Básico**: Detección de cambio de ventana/tab
- **Accesibilidad**: Tiempo extendido para usuarios con necesidades especiales
- **Recovery**: Reanudación automática tras desconexión < 30min

---

## 4. Corrección Manual (Override)

### 4.1 Descripción del Proceso
Flujo para que instructores revisen y ajusten calificaciones automáticas, proporcionando feedback adicional y asegurando calidad en la evaluación.

### 4.2 Vista Backend/DevOps

| # | Actor | Acción | SLA | Auditoría |
|---|-------|--------|-----|-----------|
| 1 | Instructor | `GET /attempts/{id}` para revisar respuestas | ≤ 200ms | Log de acceso |
| 2 | Instructor | `PATCH /answers/{id}` modifica `ai_score` | ≤ 150ms | Diff completo |
| 3 | Core API | Recalcula `total_score` del intento | ≤ 100ms | Propagación automática |
| 4 | Core API | Registra cambio en `AuditLogs` | ≤ 50ms | Trazabilidad completa |
| 5 | Notification Worker | Envía notificación al candidato | ≤ 1min | Email + push opcional |

### 4.3 Vista Frontend/Usuario

| Paso | Pantalla | Acción del Instructor | Feedback Visual | Resultado |
|------|---------|----------------------|-----------------|-----------|
| A | Panel Revisión | Selecciona intento para revisar | Lista filtrable por score/fecha | Navigation |
| B | Detalle Respuesta | Ve respuesta + score IA + rúbrica | Score original destacado | Review |
| C | Edición Score | Ajusta puntuación con slider/input | Campo cambia a amarillo "Overridden" | Edit |
| D | Justificación | Añade comentario obligatorio | Textarea con contador de caracteres | Input |
| E | Confirmación | Click "Guardar Cambios" | Toast "Cambios aplicados" + timestamp | Save |

**Candidato recibe:**
- Email con notificación de actualización
- Link directo a feedback actualizado
- Explicación del cambio realizado

---

## 5. Upgrade/Downgrade de Plan

### 5.1 Descripción del Proceso
Gestión automática de cambios de suscripción integrada con Stripe, incluyendo prorrateado, actualización de límites y notificaciones.

### 5.2 Vista Backend/DevOps

| # | Servicio | Acción | SLA | Integración |
|---|----------|--------|-----|-------------|
| 1 | Admin Tenant | Click "Upgrade Plan" → redirect Stripe Checkout | Inmediato | Stripe Session |
| 2 | Stripe | Procesa pago + envía webhook `checkout.session.completed` | < 3s | Webhook validation |
| 3 | Core API | Actualiza `Tenants.plan` y límites asociados | ≤ 200ms | DB transaction |
| 4 | Core API | Calcula prorrateado y créditos aplicables | ≤ 500ms | Stripe API |
| 5 | Mail Worker | Envía confirmación + factura PDF | ≤ 1min | Template generation |
| 6 | Metrics Service | Actualiza métricas de billing y usage | ≤ 100ms | Analytics update |

### 5.3 Vista Frontend/Usuario

| Paso | Pantalla | Acción del Usuario | Feedback | Estado |
|------|---------|-------------------|----------|--------|
| A | Billing Dashboard | Compara planes + click "Upgrade" | Tabla comparativa destacada | Selection |
| B | Stripe Checkout | Completa información de pago | Formulario seguro de Stripe | Payment |
| C | Procesamiento | Espera confirmación de pago | Spinner + "Procesando pago..." | Processing |
| D | Redirect Back | Regresa a dashboard actualizado | Banner verde "Plan actualizado" | Success |
| E | Confirmación | Ve nuevos límites y características | Métricas actualizadas en tiempo real | Complete |

### 5.4 Casos Especiales
- **Downgrade con sobreuso**: Grace period de 24h + alertas
- **Pago fallido**: Retry automático + notificaciones escaladas
- **Cancelación**: Acceso hasta final del período pagado

---

## 6. Backup y Restore

### 6.1 Descripción del Proceso
Sistema automatizado de backups con capacidad de restauración granular, diseñado para minimizar RTO y RPO según SLAs empresariales.

### 6.2 Vista Backend/DevOps

| # | Actor | Acción | SLA | Verificación |
|---|-------|--------|-----|--------------|
| 1 | Cron Job | Ejecuta `pg_dump` con compresión gzip | 02:00 UTC | Snapshot consistency |
| 2 | Backup Script | Calcula y verifica checksum SHA-256 | +5min | Integridad de datos |
| 3 | Storage Service | Copia a múltiples regiones (3-2-1 rule) | +10min | Redundancia geográfica |
| 4 | Core API | Registra entrada en tabla `Backups` | +1min | Metadata completa |
| 5 | Monitoring | Valida éxito y envía alertas si falla | +2min | Alerting automático |

**Para Restore:**
| # | Actor | Acción | SLA | Precauciones |
|---|-------|--------|-----|--------------|
| 1 | SysAdmin | Selecciona backup + ejecuta dry-run | Variable | Análisis de impacto |
| 2 | Restore Service | Valida integridad + compatibilidad | ≤ 5min | Pre-flight checks |
| 3 | Database | Ejecuta restore con downtime mínimo | Variable | Blue-green strategy |
| 4 | Validation | Verifica integridad post-restore | ≤ 10min | Smoke tests |

### 6.3 Vista Frontend/Usuario

| Paso | Pantalla | Acción del SysAdmin | Feedback | Seguridad |
|------|---------|-------------------|----------|-----------|
| A | Console Admin | Ve lista de backups con status | Badges OK/FAIL + timestamps | Read-only view |
| B | Backup Detail | Examina metadata + click "Restore" | Modal de confirmación crítico | MFA required |
| C | Dry Run | Revisa impacto estimado | Lista de cambios + warnings | Impact analysis |
| D | Confirmación | Confirma con justificación | Input obligatorio + doble confirmación | Audit trail |
| E | Progreso | Monitorea restore 0-100% | Progress bar + logs en tiempo real | Live monitoring |
| F | Validación | Recibe confirmación de éxito/fallo | Banner con resultado + next steps | Status clear |

---

## 7. Consideraciones Transversales

### 7.1 Manejo de Errores
- **Retry Logic**: Exponential backoff para servicios externos
- **Circuit Breakers**: Protección contra cascading failures
- **Graceful Degradation**: Funcionalidad reducida vs. fallo completo
- **User Communication**: Mensajes claros sobre estado y próximos pasos

### 7.2 Monitoreo y Observabilidad
- **Distributed Tracing**: Seguimiento end-to-end de requests
- **Custom Metrics**: KPIs específicos por flujo de negocio
- **Real-time Dashboards**: Visibilidad operacional continua
- **Alerting**: Notificaciones proactivas basadas en SLAs

### 7.3 Seguridad en Flujos
- **Input Validation**: Sanitización en todos los puntos de entrada
- **Rate Limiting**: Protección contra abuse y DoS
- **Audit Logging**: Trazabilidad completa de acciones críticas
- **Data Encryption**: Protección en tránsito y en reposo

---

## 6. Experiencia del Usuario - Dashboard y Navegación

### 6.1 Descripción del Proceso
Flujo completo de la experiencia del usuario desde el login hasta la navegación por cursos, incluyendo dashboard principal con navegación lateral y estructura modular de cursos.

### 6.2 Vista Frontend/UX

```mermaid
flowchart TD
    A[Usuario inicia sesión] --> B[Validación de credenciales]
    B --> C[Carga dashboard con sidebar]

    C --> D[Sidebar Navigation]
    C --> E[Main Content Area]

    D --> D1[Home - activo]
    D --> D2[Marketplace]
    D --> D3[Mis Cursos]
    D --> D4[Guardados]
    D --> D5[Analíticas]
    D --> D6[Perfil]
    D --> D7[Configuración]
    D --> D8[Administración - condicional]

    E --> E1[Stats Cards: Completados, En Progreso, Guardados, Racha]
    E --> E2[Accedidos Recientemente con progreso visual]
    E --> E3[Recomendados para Ti con botón guardar]
    E --> E4[Cursos Guardados con grid layout]
    E --> E5[Actividad Reciente con timestamps]

    E2 --> F[Continuar curso]
    E3 --> G[Explorar/Guardar curso]
    E4 --> H[Acceder curso guardado]

    F --> I[Estructura de Curso]
    G --> I
    H --> I

    I --> I1[Múltiples Módulos/Secciones]
    I1 --> I2[Cada módulo: contenido + tests opcionales]
    I2 --> I3[Progreso por módulo y curso total]
    I3 --> I4[Navegación secuencial o libre]
```

### 6.3 Estructura de Cursos y Módulos

| Componente | Descripción | Funcionalidad |
|------------|-------------|---------------|
| **Curso** | Contenedor principal | Título, descripción, nivel de dificultad, horas estimadas |
| **Módulos** | Secciones dentro del curso | Orden configurable, requerido/opcional, progreso independiente |
| **Contenido** | Material dentro de módulos | Videos, documentos, assignments, tests, lecturas |
| **Tests** | Evaluaciones por módulo | Opcionales o requeridos, múltiples tipos, scoring automático |
| **Progreso** | Tracking granular | Por contenido, por módulo, por curso, con porcentajes |

### 6.4 Estados de Navegación

```mermaid
stateDiagram-v2
    [*] --> Dashboard
    Dashboard --> Marketplace
    Dashboard --> MisCursos
    Dashboard --> Guardados
    Dashboard --> Perfil
    Dashboard --> Administracion

    MisCursos --> CursoDetalle
    Marketplace --> CursoDetalle
    Guardados --> CursoDetalle

    CursoDetalle --> ModuloDetalle
    ModuloDetalle --> ContenidoDetalle
    ModuloDetalle --> TestDetalle

    ContenidoDetalle --> ModuloDetalle
    TestDetalle --> ModuloDetalle
    ModuloDetalle --> CursoDetalle
    CursoDetalle --> Dashboard
```

### 6.5 Métricas de UX

| Métrica | Target | Medición |
|---------|--------|----------|
| **Tiempo de carga dashboard** | < 3s | Time to Interactive |
| **Navegación entre secciones** | < 500ms | Route transition time |
| **Actualización de progreso** | Real-time | WebSocket updates |
| **Búsqueda en marketplace** | < 1s | Search response time |
| **Guardado de preferencias** | < 200ms | Auto-save functionality |

---

## 7. Administración de Cursos con Permisos Granulares

### 7.1 Descripción del Proceso
Flujo completo para la administración de cursos con sistema de colaboradores, valoraciones y reportes.

### 7.2 Vista Frontend/UX

```mermaid
flowchart TD
    A[Usuario con permisos de curso] --> B[Accede a Administrar Cursos]
    B --> C[Vista Grid de Cursos]

    C --> D[Filtros y Búsqueda]
    C --> E[Crear Nuevo Curso - si tiene permiso]
    C --> F[Lista de Cursos con Roles]

    F --> F1[Creador - Verde]
    F --> F2[Colaborador - Azul]
    F --> F3[Calificador - Amarillo]

    F --> G[Acciones por Curso]
    G --> G1[Administrar/Calificar]
    G --> G2[Like/Dislike]
    G --> G3[Reportar]

    G1 --> H[Herramientas de Administración]
    H --> H1[Editar Módulos - si es Creador/Colaborador]
    H --> H2[Ver Respuestas - si puede calificar]
    H --> H3[Gestionar Colaboradores - si es Creador]
    H --> H4[Analíticas del Curso]

    G3 --> I[Modal de Reporte]
    I --> I1[Seleccionar Motivo]
    I --> I2[Comentario Detallado]
    I --> I3[Enviar a Moderación]
```

### 7.3 Sistema de Colaboración

| Rol | Permisos | Acciones Disponibles |
|-----|----------|---------------------|
| **Creador** | Todos los permisos | Editar contenido, gestionar colaboradores, calificar, ver analíticas, eliminar curso |
| **Colaborador** | Edición y calificación | Editar módulos, calificar exámenes, ver respuestas, analíticas limitadas |
| **Calificador** | Solo calificación | Calificar exámenes, ver respuestas de estudiantes, feedback |

### 7.4 Estados de Valoración y Moderación

```mermaid
stateDiagram-v2
    [*] --> SinValorar
    SinValorar --> Like
    SinValorar --> Dislike
    Like --> Dislike
    Dislike --> Like
    Like --> SinValorar
    Dislike --> SinValorar

    [*] --> SinReportar
    SinReportar --> Reportado
    Reportado --> EnRevision
    EnRevision --> Resuelto
    EnRevision --> Desestimado
```

---

## 8. Sistema de Foros y Expertos

### 8.1 Descripción del Proceso
Flujo completo para foros de cursos con sistema de expertos y mensajes destacados.

### 8.2 Flujo de Foro por Curso

```mermaid
flowchart TD
    A[Usuario accede al curso] --> B[Botón "Ir al Foro"]
    B --> C[Vista del Foro del Curso]

    C --> D[Mensajes Destacados de Expertos]
    C --> E[Posts Regulares por Categoría]
    C --> F[Crear Nueva Publicación]

    D --> D1[Mensaje con Highlight Especial]
    D --> D2[Badge de Experto Visible]
    D --> D3[Posición Fija en la Parte Superior]

    E --> E1[Posts por Categoría]
    E --> E2[Sistema de Likes]
    E --> E3[Respuestas y Threads]

    F --> F1[Modal de Nueva Publicación]
    F1 --> F2[Título + Contenido + Categoría]
    F2 --> F3[Publicar en Foro]

    G[Usuario Experto] --> H[Privilegios Especiales]
    H --> H1[Crear Mensaje Destacado]
    H --> H2[Destacar Posts Existentes]
    H --> H3[Moderar Contenido]
```

### 8.3 Sistema de Expertos

| Acción | Quién Puede | Requisitos | Resultado |
|--------|-------------|------------|-----------|
| **Asignar Experto** | Admin Tenant | Usuario válido + Área específica | Usuario obtiene rol de experto |
| **Crear Review** | Experto | Curso en su área + No tener review previo | Review pendiente de verificación |
| **Verificar Review** | Admin Tenant | Review existente | Review visible en curso |
| **Destacar Mensaje** | Experto | Foro de curso en su área | Mensaje aparece destacado |
| **Moderar Foro** | Experto | Contenido inapropiado en su área | Contenido removido/editado |

### 8.4 Estados de Expert Review

```mermaid
stateDiagram-v2
    [*] --> Creado
    Creado --> PendienteVerificacion
    PendienteVerificacion --> Verificado
    PendienteVerificacion --> Rechazado
    Verificado --> Publicado
    Rechazado --> [*]
    Publicado --> Editado
    Editado --> PendienteVerificacion
```

### 8.5 Tipos de Rating de Expertos

| Rating | Valor | Color | Descripción |
|--------|-------|-------|-------------|
| **Muy Malo** | 1 | Rojo | Curso con problemas graves |
| **Malo** | 2 | Naranja | Curso con deficiencias importantes |
| **Neutral** | 3 | Gris | Curso promedio, ni bueno ni malo |
| **Bueno** | 4 | Verde | Curso recomendable con calidad |
| **Excelente** | 5 | Dorado | Curso excepcional, altamente recomendado |

---

## Conclusión

Estos flujos clave representan los procesos más críticos de Arroyo University, diseñados para proporcionar una experiencia de usuario fluida mientras mantienen robustez técnica y observabilidad operacional. La nueva estructura de dashboard con navegación lateral y cursos modulares permite una experiencia de aprendizaje más intuitiva y personalizada, mientras que la documentación dual (backend/frontend) facilita tanto el desarrollo como el soporte operacional.
